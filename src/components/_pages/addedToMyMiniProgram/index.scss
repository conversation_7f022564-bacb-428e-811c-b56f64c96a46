.my-mini-program {
  position: fixed;
  top: 0;
  right: $spacing-h-md;
  left: $spacing-h-md;
  z-index: 999;
  transform: translateY($spacing-v-sm * 2);

  &__content {
    position: absolute;
    right: 0;
    display: flex;
    align-items: center;
    padding: $spacing-v-sm $spacing-h-md;
    color: $color-white;
    font-size: $font-size-sm;
    background-color: #000;
    border-radius: $border-radius-arc;
  }

  &__arrow {
    border: 10px solid #000;
  }

  &__hand {
    position: absolute;
    right: -20px;
    bottom: -20px;
    width: 38px;
    height: 38px;
  }

  &__btn {
    position: relative;
    margin-left: $spacing-h-md;
    padding: $spacing-v-xs $spacing-h-lg;
    font-size: $font-size-base;
    border: $width-base solid $color-white;
    border-radius: $border-radius-arc;
  }

  &__add {
    margin-right: $spacing-v-sm;
    color: #ffc100;
    font-size: $font-size-sm !important;
  }

  &__close {
    .kb-icon {
      margin-left: $spacing-v-sm;
      font-size: $font-size-xs/1.5 !important;
    }
  }

  &__help {
    position: fixed;
    top: -160px;
    right: 0;
    z-index: 999;
    width: 100%;
    height: 100%;
    background-color: rgba($color: #000000, $alpha: 0.6);

    &--tips {
      padding: $spacing-v-md 0;
    }

    &--btn {
      display: inline-block;
      padding: $spacing-v-md $spacing-h-md;
      color: #969799;
      font-size: $font-size-base;
      text-align: center;
    }

    &--wrap {
      position: absolute;
      top: 0;
      right: $spacing-h-md;
      left: $spacing-h-md;
      transform: translateY($spacing-v-sm * 2);
    }

    &--content {
      position: absolute;
      top: 0;
      right: 0;
      padding: 0 $spacing-h-lg;
      text-align: center;
      background-color: $color-white;
      border-radius: 20px;
    }

    &--arrow {
      border: 10px solid $color-white;
    }

    &--img {
      width: 400px;
      height: 186.667px;
      vertical-align: middle;
    }
  }

  &__arrow,
  &__help--arrow {
    position: absolute;
    top: -10px;
    left: 660px;
    width: 0;
    height: 0;
    border-top-width: 0;
    border-right-color: transparent;
    border-left-color: transparent;
    transform: translateX(-1 * $spacing-h-md - 10px);
  }
}
