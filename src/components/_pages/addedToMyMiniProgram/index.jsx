import { Image, View } from '@tarojs/components';
import dayjs from 'dayjs';
import isUndefined from 'lodash/isUndefined';
import { useEffect, useMemo, useState } from '@tarojs/taro';
import { useGetMenuButtonBoundingClientRect } from '~base/components/page/nav-bar/_utils';
import { reportAnalyticsUnify } from '~base/utils/utils';
import { useStorage } from '~/utils/storage';
import { useCheckIsAddedToMyMiniProgram } from './_utils';
import './index.scss';

const AddedToMyMiniProgram = () => {
  const [isOpened, setIsOpened] = useState(false);
  const { data, update } = useStorage('addedToMyMiniProgram');

  const { start, stop, check } = useCheckIsAddedToMyMiniProgram();

  const bounding = useGetMenuButtonBoundingClientRect();
  const styles = useMemo(() => {
    const { top, height, left, width } = bounding;
    return {
      wrap: {
        top: `${top + height}px`,
      },
      arrow: {
        left: `${left + width / 4}px`,
      },
    };
  }, [bounding]);

  const handleClose = () => {
    // 关闭
    setIsOpened(false);
  };

  useEffect(() => {
    if (!isUndefined(data)) {
      // 非undefined，说明获取完成
      const { ts } = data || {};
      const isSameDay = ts && dayjs(ts).isSame(dayjs(), 'day');
      if (isSameDay) {
        return;
      }
      check().then(({ added }) => {
        setIsOpened(!added);

        if (!added) {
          // 循环检查，是否添加
          start().then(({ added: newAdded }) => {
            if (newAdded) {
              // 已添加
              setIsOpened(false);
              reportAnalyticsUnify({
                action: 'addedToMyMiniProgram',
              });
            }
          });
        }
      });
      update('1');
    }
  }, [data]);

  useEffect(() => {
    return () => {
      stop();
    };
  }, []);

  return isOpened ? (
    <View className='my-mini-program__help'>
      <View className='my-mini-program__help--wrap' style={styles.wrap}>
        <View className='my-mini-program__help--arrow' style={styles.arrow} />
        <View className='my-mini-program__help--content'>
          <View className='my-mini-program__help--tips'>添加我的小程序，查件更方便</View>
          <View>
            <Image
              mode='widthFix'
              className='my-mini-program__help--img'
              src='https://cdn-img.kuaidihelp.com/wkd-query/add-collect2.png?v=20250408'
            />
          </View>
          <View
            className='my-mini-program__help--btn'
            hoverClass='kb-hover-opacity'
            onClick={handleClose}
          >
            我知道了，关闭
          </View>
        </View>
      </View>
    </View>
  ) : null;
};

AddedToMyMiniProgram.options = {
  addGlobalClass: false,
};

export default AddedToMyMiniProgram;
