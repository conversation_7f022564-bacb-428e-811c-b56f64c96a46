/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { useDidShowCom } from '@base/hooks/page';
import { reportAnalyticsUnify } from '@base/utils/utils';
import { Button, Image, MovableArea, MovableView, Text, View } from '@tarojs/components';
import Taro, { Fragment, useEffect, useRef, useState } from '@tarojs/taro';
import classNames from 'classnames';
import isArray from 'lodash/isArray';
import { useCheckShowDynamicsShare } from '~/utils/shareDynamics';
import isObject from 'lodash/isObject';
import { adNavigator } from '../sdk';
import { useCheckIsShieldAd } from '../sdk/shield';
import './ad-float.scss';
import { useAutoAdsorption, useCheckIsOverlapping, useMoveAreaStyles } from './_utils/float';
import { getAdConfig } from '../_utils';

const AdUnitIds =
  process.env.PLATFORM_ENV === 'weapp'
    ? {
        'query.detail': 'adunit-efef225f05c47874',
      }
    : {};

const sizeRatio = 3; // 图片倍率

/**
 *
 * @description  微信官方广告组件位/自定义广告位 - 浮动;
 * @param {{active?:boolean;closeable?:boolean;position?:string;adType?:string;closeShield?:boolean;placement?:'rt'|'rb'|'rb2'|'lt'|'lb';moveDirection?:"all" | "vertical" | "horizontal" | "none";posId?:string}} props
 * @returns
 */
const KbAdFloat = (props) => {
  const {
    active = true,
    adType,
    position,
    closeShield,
    placement = 'rt',
    closeable = true,
    moveDirection = 'all',
  } = props || {};
  const unitId = AdUnitIds[adType];
  const [imgUrl, updateImgUrl] = useState('');
  const [size, updateSize] = useState({});
  const actionRef = useRef(); //当前广告数据
  const actionRefAds = useRef(); //全部广告数据
  const refreshRef = useRef(false); //刷新标志
  const adWrapId = 'kb-ad-float__content'
  const { isShow: isShowDynamicsShare, update: updateCheckShowDynamicsShare } =
    useCheckShowDynamicsShare();

  const { posReady, styles: moveAreaStyles, coverData } = useMoveAreaStyles(props);
  const isOverlapping = useCheckIsOverlapping({
    coverData,
    selector: `.kb-query>>>#${adWrapId}`,
  });
  // 自动吸附
  const {
    posX,
    posY,
    onChange: handleChange,
    onTouchEnd: handleTouchEnd,
    updateImageSize,
  } = useAutoAdsorption(isOverlapping, coverData, {
    selector: `.kb-query>>>#${adWrapId}`,
    checkCollisionOnDrag: true,
  });

  useEffect(() => {
    if (!!adType) return;
    const params = position
      ? isObject(position)
        ? position
        : { position }
      : [{ type: 'record' }, { type: '1' }];
    getAdConfig(params).then((adRes) => {
      const list = isArray(adRes) ? adRes : adRes.list;
      if (isArray(list) && list.length > 0) {
        actionRefAds.current = {
          list,
          cIndex: 0,
        };
        getCurrentAd();
      }
    });
  }, [adType, position]);

  useDidShowCom(() => {
    if (refreshRef.current) {
      getCurrentAd('switch');
      refreshRef.current = false;
    }
  });

  const getCurrentAd = (action = 'init') => {
    let { list, cIndex } = actionRefAds.current;
    if (action == 'switch' && list.length <= 1) return;
    if (cIndex > list.length - 1) {
      cIndex = 0;
    }
    const data = list[cIndex];
    actionRef.current = data;
    const { imgUrl, title } = data || {};

    updateCheckShowDynamicsShare(data);

    if (imgUrl) {
      reportAnalyticsUnify({
        title,
        position: position || adType,
        action: 'ad-switch',
      });
      Taro.getImageInfo({
        src: imgUrl,
        success: (res) => {
          const { width: orgWidth, height: orgHeight } = res;
          const width = orgWidth / sizeRatio;
          const height = orgHeight / sizeRatio;
          updateImageSize({ width, height });
          updateSize({ width: `${width}px`, height: `${height}px` });
          updateImgUrl(imgUrl);
        },
      });
    }
    actionRefAds.current.cIndex = cIndex + 1;
  };

  // 关闭广告
  const handleClose = () => updateImgUrl('');

  // 点击广告
  const handleClickAd = () => {
    adNavigator(actionRef.current);
    refreshRef.current = true;
  };

  // 官方流量主广告展示统计
  const onAdLoad = () => {
    reportAnalyticsUnify({
      title: unitId,
      action: 'load',
    });
  };

  const shield1 = useCheckIsShieldAd(closeShield ? '-1' : '1'); // 是否屏蔽流量主广告
  const hasAd = active && posReady && !shield1 && (imgUrl || unitId); // 传入 selector，需要等待selector位置获取成功才展示广告

  const moveDisabled = !moveDirection || moveDirection === 'none';
  const rootCls = classNames('kb-ad-float', `kb-ad-float__placement--${placement}`);

  return hasAd ? (
    <MovableArea className='kb-ad-float__area' style={moveAreaStyles}>
      <MovableView
        disabled={moveDisabled}
        className={rootCls}
        style={size}
        direction={moveDirection}
        inertia
        x={posX}
        y={posY}
        animation
        friction={2}
        onChange={handleChange}
        onTouchEnd={handleTouchEnd}
      >
        {!!imgUrl && (
          <Fragment>
            <View id={adWrapId} hoverClass='kb-hover-opacity' onClick={handleClickAd}>
              {isShowDynamicsShare && (
                <Button className='kb-ad-float__share--btn' openType='share'></Button>
              )}
              <Image
                lazyLoad
                className='kb-ad-float__image'
                mode='widthFix'
                src={imgUrl}
                style={size}
              />
            </View>
            {closeable && (
              <View
                className='kb-ad-float__close'
                hoverClass='kb-hover-opacity'
                onClick={handleClose}
              >
                <Text className='kb-icon kb-icon-wrong' />
              </View>
            )}
          </Fragment>
        )}
        {!!unitId && (
          <View hoverClass='kb-hover-opacity' className='kb-ad-float__guid'>
            <ad-custom unitId={unitId} style='height:auto;' onLoad={onAdLoad} />
          </View>
        )}
      </MovableView>
    </MovableArea>
  ) : (
    <Fragment />
  );
};
KbAdFloat.options = {
  addGlobalClass: true,
};
export default KbAdFloat;
