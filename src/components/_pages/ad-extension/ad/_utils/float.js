import Taro, { useCallback, useEffect, useMemo, useRef, useState } from '@tarojs/taro';
import debounce from 'lodash/debounce';
import { useGetMenuButtonBoundingClientRect } from '~base/components/page/nav-bar/_utils';
import { useBoundingClientRect } from '~base/hooks/observer';
import { useCheckIsTabPage } from '~base/utils/navigator';
import { getSystemInfoSync } from '~base/utils/utils';

/**
 *
 * @description 自动吸附
 */
export function useAutoAdsorption(isOverlapping, coverData, { selector, checkCollisionOnDrag = true } = {}) {
  const ref = useRef({
    curX: 0,
    curY: 300,
    originalY: 300,       // 记录原始Y位置（未调整前的位置）
    imageSize: { width: 0, height: 0 },
  });
  const [posX, setPosX] = useState(ref.current.curX);
  const [posY, setPosy] = useState(ref.current.curY);

  // 拖拽触摸触屏移动
  const onChange = (e) => {
    const { x, y } = e.detail;
    ref.current.curX = x;
    ref.current.curY = y;
  };

  // 碰撞检测函数，计算需要调整的距离
  const checkCollision = useCallback((floatRect) => {
    console.log(coverData, floatRect, 'checkCollision');
    if (!coverData || !floatRect) return 0;

    const { top: floatTop, height: floatHeight } = floatRect;
    const { top: targetTop, height: targetHeight } = coverData;

    if (
      floatTop !== undefined &&
      floatHeight !== undefined &&
      targetTop !== undefined &&
      targetHeight !== undefined
    ) {
      const floatBottom = floatTop + floatHeight;
      const targetBottom = targetTop + targetHeight;

      // 检查是否有重叠
      if (floatBottom > targetTop && floatTop < targetBottom) {
        // 计算需要移动的距离来完全避开重叠
        const floatCenter = floatTop + floatHeight / 2;
        const targetCenter = targetTop + targetHeight / 2;

        let moveDistance;
        if (floatCenter > targetCenter) {
          // 浮动元素中心在目标元素下方，向下移动到目标元素底部
          moveDistance = targetBottom - floatTop;
        } else {
          // 浮动元素中心在目标元素上方，向上移动到目标元素顶部
          moveDistance = targetTop - floatBottom;
        }

        return moveDistance;
      }
    }
    return 0;
  }, [coverData]);

  // 应用位置调整
  const applyPositionAdjustment = useCallback((hasCollision) => {
    console.log('应用位置调整:', { hasCollision, originalY: ref.current.originalY });

    if (!hasCollision) {
      // 没有碰撞，返回原始位置
      console.log('没有碰撞，恢复到原始位置:', ref.current.originalY);
      setPosy(ref.current.originalY);
      return;
    }

    // 有碰撞，计算调整位置
    const currentFloatRect = {
      top: ref.current.originalY,
      height: ref.current.imageSize.height || 100
    };

    const moveDistance = checkCollision(currentFloatRect);
    console.log('计算移动距离:', moveDistance);

    if (moveDistance !== 0) {
      const spacing = 10;
      const adjustment = moveDistance > 0 ? moveDistance + spacing : moveDistance - spacing;
      const newY = ref.current.originalY + adjustment;
      console.log('调整位置:', { moveDistance, adjustment, newY });
      setPosy(newY);
    }
  }, [checkCollision]);

  // 拖拽后碰撞检测
  const handleDragCollisionCheck = useCallback(() => {
    if (!checkCollisionOnDrag || !selector) return;

    // 延迟检测，确保DOM更新完成
    setTimeout(() => {
      const query = Taro.createSelectorQuery();
      query.select(selector).boundingClientRect();
      query.exec((res) => {
        if (res && res[0]) {
          const moveDistance = checkCollision(res[0]);
          const hasCollision = moveDistance !== 0;
          console.log('拖拽后碰撞检测:', { hasCollision, moveDistance, originalY: ref.current.originalY });

          if (hasCollision) {
            const spacing = 10;
            const adjustment = moveDistance > 0 ? moveDistance + spacing : moveDistance - spacing;
            const newY = ref.current.originalY + adjustment;
            console.log('拖拽后调整位置:', { moveDistance, adjustment, newY });
            setPosy(newY);
          }
        }
      });
    }, 50);
  }, [checkCollisionOnDrag, selector, checkCollision]);

  // 触摸触屏结束
  const onTouchEnd = () => {
    const { curX, curY } = ref.current;
    const { windowWidth: screenWidth } = getSystemInfoSync();
    const threshold = screenWidth / 2; // 设定屏幕中线

    // 更新原始位置为当前拖拽位置
    ref.current.originalY = curY;

    // 判断靠左还是靠右，并吸附
    setPosX(curX);
    setPosy(curY);

    // 拖拽结束后检测碰撞
    handleDragCollisionCheck();

    setTimeout(() => {
      const { width: imgWidth } = ref.current.imageSize;
      const elementCenterX = curX + imgWidth / 2;
      setPosX(elementCenterX < threshold ? 0 : screenWidth - imgWidth);
    }, 150);
  };

  // 更新图片尺寸
  const updateImageSize = (size) => {
    ref.current.imageSize = {
      ...ref.current.imageSize,
      ...size,
    };
  };

  // 监听覆盖状态变化，应用位置调整
  useEffect(() => {
    console.log('覆盖状态变化:', { isOverlapping, originalY: ref.current.originalY });
    applyPositionAdjustment(isOverlapping);
  }, [isOverlapping, coverData, applyPositionAdjustment]);

  return {
    posX,
    posY,
    updateImageSize,
    onChange,
    onTouchEnd,
  };
}

/**
 *
 * @description 可拖动区域样式，支持动态设置可移动区域
 */
export function useMoveAreaStyles(props) {
  const { selector, active = true } = props;
  const [posReady, setPosReady] = useState(!selector);
  const [coverData, setCoverData] = useState({}); // 当有selector时，需要获取selector位置，并设置patchTop

  const { top = 24, height = 32 } = useGetMenuButtonBoundingClientRect();
  const isTabPage = useCheckIsTabPage();

  const [triggerGetBounding] = useBoundingClientRect((res) => {
    setPosReady(true);
    setCoverData(res);
  });

  const triggerGetBoundingDebounce = useCallback(
    debounce(triggerGetBounding, 300, { trailing: true, leading: false }),
    [],
  );

  useEffect(() => {
    if (!selector || !active) return;
    triggerGetBoundingDebounce({ selector, component: false });
  }, [active, selector, triggerGetBoundingDebounce]);

  // moveArea样式
  const styles = useMemo(() => {
    // const { windowHeight: screenHeight } = getSystemInfoSync();
    // const _top = `${
    //   Math.max(posPatchTop, top + height) > screenHeight - 100
    //     ? top + height
    //     : Math.max(posPatchTop, top + height)
    // }px`;

    return { top: top + height + 'px', bottom: isTabPage ? '100px' : 0 };
  }, [top, height, isTabPage]);

  return {
    coverData,
    posReady,
    styles,
  };
}

export const useCheckIsOverlapping = ({ coverData, selector }) => {
  const [isOverlapping, setIsOverlapping] = useState(false); // 只返回布尔值表示是否碰撞
  const targetElementData = useRef({}); // 重命名为更清晰的变量名

  const [triggerGetBounding] = useBoundingClientRect((res) => {
    calcIsOverlapping(res);
  });

  const triggerGetBoundingDebounce = useCallback(
    debounce(triggerGetBounding, 300, { trailing: true, leading: false }),
    [],
  );

  const calcIsOverlapping = (floatElementRect) => {
    const { top: floatTop, height: floatHeight } = floatElementRect || {};
    const { top: targetTop, height: targetHeight } = targetElementData.current || {};

    console.log('浮动元素位置:', { floatTop, floatHeight });
    console.log('目标元素位置:', { targetTop, targetHeight });

    // 简化碰撞检测逻辑：只检测是否有重叠
    if (
      floatTop !== undefined &&
      floatHeight !== undefined &&
      targetTop !== undefined &&
      targetHeight !== undefined
    ) {
      const floatBottom = floatTop + floatHeight;
      const targetBottom = targetTop + targetHeight;

      // 检查是否有重叠
      const hasOverlap = floatBottom > targetTop && floatTop < targetBottom;
      console.log('碰撞检测结果:', hasOverlap);
      setIsOverlapping(hasOverlap);
    } else {
      setIsOverlapping(false);
    }
  };

  useEffect(() => {
    if (coverData) {
      targetElementData.current = coverData;
      triggerGetBoundingDebounce({
        selector,
        component: false,
      });
    }
  }, [selector, coverData, triggerGetBoundingDebounce]);

  return isOverlapping;
};
