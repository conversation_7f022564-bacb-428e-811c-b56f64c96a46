import { useCallback, useEffect, useMemo, useRef, useState } from '@tarojs/taro';
import debounce from 'lodash/debounce';
import { useGetMenuButtonBoundingClientRect } from '~base/components/page/nav-bar/_utils';
import { useBoundingClientRect } from '~base/hooks/observer';
import { useCheckIsTabPage } from '~base/utils/navigator';
import { getSystemInfoSync } from '~base/utils/utils';

/**
 *
 * @description 自动吸附
 */
export function useAutoAdsorption(isOverlapping, coverData) {
  const ref = useRef({ curX: 0, curY: 0, imageSize: { width: 0, height: 0 } }); // 移动位置以及图片尺寸
  const [posX, setPosX] = useState(ref.current.curX);
  const [posY, setPosy] = useState(ref.current.curY);

  // 拖拽触摸触屏移动
  const onChange = (e) => {
    const { x, y } = e.detail;
    ref.current.curX = x;
    ref.current.curY = y;
  };

  // 触摸触屏结束
  const onTouchEnd = () => {
    const { curX, curY } = ref.current;
    const { windowWidth: screenWidth } = getSystemInfoSync();
    const threshold = screenWidth / 2; // 设定屏幕中线
    // 判断靠左还是靠右，并吸附
    setPosX(curX);
    setPosy(curY);
    console.log(coverData, '-----coverData');

    setTimeout(() => {
      const { width: imgWidth } = ref.current.imageSize;
      const elementCenterX = curX + imgWidth / 2;
      setPosX(elementCenterX < threshold ? 0 : screenWidth - imgWidth);
    }, 150);
  };

  // 更新图片尺寸
  const updateImageSize = (size) => {
    ref.current.imageSize = {
      ...ref.current.imageSize,
      ...size,
    };
  };

  useEffect(() => {
    console.log(isOverlapping, coverData, 'watch-----isOverlapping');
    if (isOverlapping) {
      setPosy((pre) => {
        return pre - 100;
      });
    }
  }, [isOverlapping, coverData]);

  return {
    posX,
    posY,
    updateImageSize,
    onChange,
    onTouchEnd,
  };
}

/**
 *
 * @description 可拖动区域样式，支持动态设置可移动区域
 */
export function useMoveAreaStyles(props) {
  const { selector, active = true } = props;
  const [posReady, setPosReady] = useState(!selector);
  const [coverData, setCoverData] = useState({}); // 当有selector时，需要获取selector位置，并设置patchTop

  const { top = 24, height = 32 } = useGetMenuButtonBoundingClientRect();
  const isTabPage = useCheckIsTabPage();

  const [triggerGetBounding] = useBoundingClientRect((res) => {
    setPosReady(true);
    setCoverData(res);
  });

  const triggerGetBoundingDebounce = useCallback(
    debounce(triggerGetBounding, 300, { trailing: true, leading: false }),
    [],
  );

  useEffect(() => {
    if (!selector || !active) return;
    triggerGetBoundingDebounce({ selector, component: false });
  }, [active, selector, triggerGetBoundingDebounce]);

  // moveArea样式
  const styles = useMemo(() => {
    // const { windowHeight: screenHeight } = getSystemInfoSync();
    // const _top = `${
    //   Math.max(posPatchTop, top + height) > screenHeight - 100
    //     ? top + height
    //     : Math.max(posPatchTop, top + height)
    // }px`;

    return { top: top + height + 'px', bottom: isTabPage ? '100px' : 0 };
  }, [top, height, isTabPage]);

  return {
    coverData,
    posReady,
    styles,
  };
}

export const useCheckIsOverlapping = ({ coverData, selector }) => {
  const [isOverlapping, setIsOverlapping] = useState(false);
  const targetElementData = useRef({}); // 重命名为更清晰的变量名

  const [triggerGetBounding] = useBoundingClientRect((res) => {
    calcIsOverlapping(res);
  });

  const triggerGetBoundingDebounce = useCallback(
    debounce(triggerGetBounding, 300, { trailing: true, leading: false }),
    [],
  );

  const calcIsOverlapping = (floatElementRect) => {
    const { top: floatTop, height: floatHeight } = floatElementRect || {};
    const { top: targetTop, height: targetHeight } = targetElementData.current || {};

    console.log('浮动元素位置:', { floatTop, floatHeight });
    console.log('目标元素位置:', { targetTop, targetHeight });

    // 修正碰撞检测逻辑：
    // 1. 浮动元素底部 > 目标元素顶部 且 浮动元素顶部 < 目标元素底部
    // 2. 确保所有必要的数据都存在
    if (
      floatTop !== undefined &&
      floatHeight !== undefined &&
      targetTop !== undefined &&
      targetHeight !== undefined
    ) {
      const floatBottom = floatTop + floatHeight;
      const targetBottom = targetTop + targetHeight;

      const _isOverlapping = floatBottom > targetTop && floatTop < targetBottom;
      setIsOverlapping(_isOverlapping);
    } else {
      setIsOverlapping(false);
    }
  };

  useEffect(() => {
    if (coverData) {
      targetElementData.current = coverData;
      triggerGetBoundingDebounce({
        selector,
        component: false,
      });
    }
  }, [selector, coverData, triggerGetBoundingDebounce]);

  return isOverlapping;
};
