import Taro, { useCallback, useEffect, useMemo, useRef, useState } from '@tarojs/taro';
import debounce from 'lodash/debounce';
import { useGetMenuButtonBoundingClientRect } from '~base/components/page/nav-bar/_utils';
import { useBoundingClientRect } from '~base/hooks/observer';
import { useCheckIsTabPage } from '~base/utils/navigator';
import { getSystemInfoSync } from '~base/utils/utils';

/**
 *
 * @description 自动吸附
 */
export function useAutoAdsorption(isOverlapping, coverData, { selector, checkCollisionOnDrag = true } = {}) {
  const ref = useRef({
    curX: 0,
    curY: 0,
    imageSize: { width: 0, height: 0 },
    lastAdjustment: 0, // 记录上次调整的偏移量
    isAdjusted: false  // 记录是否已经调整过位置
  });
  const [posX, setPosX] = useState(ref.current.curX);
  const [posY, setPosy] = useState(ref.current.curY);

  // 拖拽触摸触屏移动
  const onChange = (e) => {
    const { x, y } = e.detail;
    ref.current.curX = x;
    ref.current.curY = y;
  };

  // 碰撞检测函数，复用useCheckIsOverlapping的逻辑
  const checkCollision = useCallback((floatRect) => {
    console.log()
    if (!coverData || !floatRect) return 0;

    const { top: floatTop, height: floatHeight } = floatRect;
    const { top: targetTop, height: targetHeight } = coverData;

    if (
      floatTop !== undefined &&
      floatHeight !== undefined &&
      targetTop !== undefined &&
      targetHeight !== undefined
    ) {
      const floatBottom = floatTop + floatHeight;
      const targetBottom = targetTop + targetHeight;

      // 检查是否有重叠
      if (floatBottom > targetTop && floatTop < targetBottom) {
        // 计算覆盖高度
        const overlapHeight = Math.min(floatBottom, targetBottom) - Math.max(floatTop, targetTop);

        // 判断浮动元素相对于目标元素的位置
        const floatCenter = floatTop + floatHeight / 2;
        const targetCenter = targetTop + targetHeight / 2;

        return floatCenter > targetCenter ? overlapHeight : -overlapHeight;
      }
    }
    return 0;
  }, [coverData]);

  // 应用位置调整
  const applyPositionAdjustment = useCallback((overlapHeight) => {
    if (overlapHeight === 0) {
      // 没有覆盖，如果之前有调整过，需要恢复
      if (ref.current.isAdjusted) {
        setPosy(prevY => prevY - ref.current.lastAdjustment);
        ref.current.lastAdjustment = 0;
        ref.current.isAdjusted = false;
      }
      return;
    }

    // 有覆盖，计算新的调整量
    const newAdjustment = overlapHeight > 0 ? overlapHeight + 10 : overlapHeight - 10;

    setPosy(prevY => {
      // 如果之前有调整过，先恢复再应用新的调整
      const baseY = ref.current.isAdjusted ? prevY - ref.current.lastAdjustment : prevY;
      ref.current.lastAdjustment = newAdjustment;
      ref.current.isAdjusted = true;
      return baseY + newAdjustment;
    });
  }, []);

  // 拖拽后碰撞检测
  const handleDragCollisionCheck = useCallback(() => {
    if (!checkCollisionOnDrag || !selector) return;

    const query = Taro.createSelectorQuery();
    query.select(selector).boundingClientRect();
    query.exec((res) => {
      if (res && res[0]) {
        const collisionHeight = checkCollision(res[0]);
        applyPositionAdjustment(collisionHeight);
      }
    });
  }, [checkCollisionOnDrag, selector, checkCollision, applyPositionAdjustment]);

  // 触摸触屏结束
  const onTouchEnd = () => {
    const { curX, curY } = ref.current;
    const { windowWidth: screenWidth } = getSystemInfoSync();
    const threshold = screenWidth / 2; // 设定屏幕中线

    // 判断靠左还是靠右，并吸附
    setPosX(curX);
    setPosy(curY);

    // 拖拽结束后检测碰撞
    handleDragCollisionCheck();

    setTimeout(() => {
      const { width: imgWidth } = ref.current.imageSize;
      const elementCenterX = curX + imgWidth / 2;
      setPosX(elementCenterX < threshold ? 0 : screenWidth - imgWidth);
    }, 150);
  };

  // 更新图片尺寸
  const updateImageSize = (size) => {
    ref.current.imageSize = {
      ...ref.current.imageSize,
      ...size,
    };
  };

  // 监听覆盖状态变化，应用位置调整
  useEffect(() => {
    console.log(isOverlapping, coverData, 'watch-----isOverlapping');
    applyPositionAdjustment(isOverlapping);
  }, [isOverlapping, coverData, applyPositionAdjustment]);

  return {
    posX,
    posY,
    updateImageSize,
    onChange,
    onTouchEnd,
  };
}

/**
 *
 * @description 可拖动区域样式，支持动态设置可移动区域
 */
export function useMoveAreaStyles(props) {
  const { selector, active = true } = props;
  const [posReady, setPosReady] = useState(!selector);
  const [coverData, setCoverData] = useState({}); // 当有selector时，需要获取selector位置，并设置patchTop

  const { top = 24, height = 32 } = useGetMenuButtonBoundingClientRect();
  const isTabPage = useCheckIsTabPage();

  const [triggerGetBounding] = useBoundingClientRect((res) => {
    setPosReady(true);
    setCoverData(res);
  });

  const triggerGetBoundingDebounce = useCallback(
    debounce(triggerGetBounding, 300, { trailing: true, leading: false }),
    [],
  );

  useEffect(() => {
    if (!selector || !active) return;
    triggerGetBoundingDebounce({ selector, component: false });
  }, [active, selector, triggerGetBoundingDebounce]);

  // moveArea样式
  const styles = useMemo(() => {
    // const { windowHeight: screenHeight } = getSystemInfoSync();
    // const _top = `${
    //   Math.max(posPatchTop, top + height) > screenHeight - 100
    //     ? top + height
    //     : Math.max(posPatchTop, top + height)
    // }px`;

    return { top: top + height + 'px', bottom: isTabPage ? '100px' : 0 };
  }, [top, height, isTabPage]);

  return {
    coverData,
    posReady,
    styles,
  };
}

export const useCheckIsOverlapping = ({ coverData, selector }) => {
  const [isOverlapping, setIsOverlapping] = useState(0); // 改为数值，表示覆盖高度
  const targetElementData = useRef({}); // 重命名为更清晰的变量名

  const [triggerGetBounding] = useBoundingClientRect((res) => {
    calcIsOverlapping(res);
  });

  const triggerGetBoundingDebounce = useCallback(
    debounce(triggerGetBounding, 300, { trailing: true, leading: false }),
    [],
  );

  const calcIsOverlapping = (floatElementRect) => {
    const { top: floatTop, height: floatHeight } = floatElementRect || {};
    const { top: targetTop, height: targetHeight } = targetElementData.current || {};

    console.log('浮动元素位置:', { floatTop, floatHeight });
    console.log('目标元素位置:', { targetTop, targetHeight });

    // 修正碰撞检测逻辑：计算覆盖高度
    // 正值表示浮动元素在目标元素下方被覆盖，需要向下调整
    // 负值表示浮动元素在目标元素上方被覆盖，需要向上调整
    if (
      floatTop !== undefined &&
      floatHeight !== undefined &&
      targetTop !== undefined &&
      targetHeight !== undefined
    ) {
      const floatBottom = floatTop + floatHeight;
      const targetBottom = targetTop + targetHeight;

      // 检查是否有重叠
      if (floatBottom > targetTop && floatTop < targetBottom) {
        console.log('有重叠');
        // 计算覆盖高度
        const overlapHeight = Math.min(floatBottom, targetBottom) - Math.max(floatTop, targetTop);

        // 判断浮动元素相对于目标元素的位置
        // 如果浮动元素中心在目标元素下方，返回正值（向下调整）
        // 如果浮动元素中心在目标元素上方，返回负值（向上调整）
        const floatCenter = floatTop + floatHeight / 2;
        const targetCenter = targetTop + targetHeight / 2;

        setIsOverlapping(floatCenter > targetCenter ? overlapHeight : -overlapHeight);
      } else {
        setIsOverlapping(0);
      }
    } else {
      setIsOverlapping(0);
    }
  };

  useEffect(() => {
    if (coverData) {
      targetElementData.current = coverData;
      triggerGetBoundingDebounce({
        selector,
        component: false,
      });
    }
  }, [selector, coverData, triggerGetBoundingDebounce]);

  return isOverlapping;
};
